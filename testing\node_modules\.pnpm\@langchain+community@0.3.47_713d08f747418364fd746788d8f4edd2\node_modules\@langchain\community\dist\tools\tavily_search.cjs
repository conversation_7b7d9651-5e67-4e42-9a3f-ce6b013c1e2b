"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TavilySearchResults = void 0;
const tools_1 = require("@langchain/core/tools");
const env_1 = require("@langchain/core/utils/env");
/**
 * Tavily search API tool integration. (Deprecated)
 *
 * @deprecated Please use the `TavilySearch` tool from the `@langchain/tavily` package, instead.
 *
 * Setup:
 * Install `@langchain/community`. You'll also need an API key set as `TAVILY_API_KEY`.
 *
 * ```bash
 * npm install @langchain/community
 * ```
 *
 * ## [Constructor args](https://api.js.langchain.com/classes/_langchain_community.tools_tavily_search.TavilySearchResults.html#constructor)
 *
 * <details open>
 * <summary><strong>Instantiate</strong></summary>
 *
 * ```typescript
 * import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
 *
 * const tool = new TavilySearchResults({
 *   maxResults: 2,
 *   // ...
 * });
 * ```
 * </details>
 *
 * <br />
 *
 * <details>
 *
 * <summary><strong>Invocation</strong></summary>
 *
 * ```typescript
 * await tool.invoke("what is the current weather in sf?");
 * ```
 * </details>
 *
 * <br />
 *
 * <details>
 *
 * <summary><strong>Invocation with tool call</strong></summary>
 *
 * ```typescript
 * // This is usually generated by a model, but we'll create a tool call directly for demo purposes.
 * const modelGeneratedToolCall = {
 *   args: {
 *     input: "what is the current weather in sf?",
 *   },
 *   id: "tool_call_id",
 *   name: tool.name,
 *   type: "tool_call",
 * };
 * await tool.invoke(modelGeneratedToolCall);
 * ```
 *
 * ```text
 * ToolMessage {
 *   "content": "...",
 *   "name": "tavily_search_results_json",
 *   "additional_kwargs": {},
 *   "response_metadata": {},
 *   "tool_call_id": "tool_call_id"
 * }
 * ```
 * </details>
 */
class TavilySearchResults extends tools_1.Tool {
    static lc_name() {
        return "TavilySearchResults";
    }
    constructor(fields) {
        super(fields);
        Object.defineProperty(this, "description", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "A search engine optimized for comprehensive, accurate, and trusted results. Useful for when you need to answer questions about current events. Input should be a search query."
        });
        Object.defineProperty(this, "name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "tavily_search_results_json"
        });
        Object.defineProperty(this, "maxResults", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 5
        });
        Object.defineProperty(this, "apiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "kwargs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        Object.defineProperty(this, "includeImages", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "includeImageDescriptions", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "includeAnswer", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "includeRawContent", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "includeDomains", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "excludeDomains", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "searchDepth", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "topic", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "days", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "apiUrl", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.maxResults = fields?.maxResults ?? this.maxResults;
        this.kwargs = fields?.kwargs ?? this.kwargs;
        this.apiKey = fields?.apiKey ?? (0, env_1.getEnvironmentVariable)("TAVILY_API_KEY");
        this.includeImages = fields?.includeImages ?? this.includeImages;
        this.includeImageDescriptions =
            fields?.includeImageDescriptions ?? this.includeImageDescriptions;
        this.includeAnswer = fields?.includeAnswer ?? this.includeAnswer;
        this.includeRawContent =
            fields?.includeRawContent ?? this.includeRawContent;
        this.includeDomains = fields?.includeDomains ?? this.includeDomains;
        this.excludeDomains = fields?.excludeDomains ?? this.excludeDomains;
        this.searchDepth = fields?.searchDepth ?? this.searchDepth;
        this.topic = fields?.topic ?? this.topic;
        this.days = fields?.days ?? this.days;
        this.apiUrl = fields?.apiUrl ?? "https://api.tavily.com";
        if (this.apiKey === undefined) {
            throw new Error(`No Tavily API key found. Either set an environment variable named "TAVILY_API_KEY" or pass an API key as "apiKey".`);
        }
    }
    async _call(input, _runManager) {
        const body = {
            query: input,
            max_results: this.maxResults,
            include_images: this.includeImages,
            include_image_descriptions: this.includeImageDescriptions,
            include_answer: this.includeAnswer,
            include_raw_content: this.includeRawContent,
            include_domains: this.includeDomains,
            exclude_domains: this.excludeDomains,
            search_depth: this.searchDepth,
            topic: this.topic,
            days: this.days,
        };
        const response = await fetch(`${this.apiUrl}/search`, {
            method: "POST",
            headers: {
                "content-type": "application/json",
                authorization: `Bearer ${this.apiKey}`,
            },
            body: JSON.stringify({ ...body, ...this.kwargs }),
        });
        const json = await response.json();
        if (!response.ok) {
            throw new Error(`Request failed with status code ${response.status}: ${json.error}`);
        }
        if (!Array.isArray(json.results)) {
            throw new Error(`Could not parse Tavily results. Please try again.`);
        }
        return JSON.stringify(json.results);
    }
}
exports.TavilySearchResults = TavilySearchResults;
