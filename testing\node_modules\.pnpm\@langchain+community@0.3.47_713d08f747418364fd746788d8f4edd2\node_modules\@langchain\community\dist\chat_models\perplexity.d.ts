import { BaseMessage } from "@langchain/core/messages";
import { BaseChatModel, BaseChatModelParams, BaseChatModelCallOptions } from "@langchain/core/language_models/chat_models";
import { CallbackManagerForLLMRun } from "@langchain/core/callbacks/manager";
import { Chat<PERSON>eneration<PERSON>hunk, Chat<PERSON><PERSON>ult } from "@langchain/core/outputs";
import { Runnable } from "@langchain/core/runnables";
import { BaseLanguageModelInput, StructuredOutputMethodOptions } from "@langchain/core/language_models/base";
import { InteropZodType } from "@langchain/core/utils/types";
/**
 * Type representing the role of a message in the Perplexity chat model.
 */
export type PerplexityRole = "system" | "user" | "assistant";
/**
 * Interface defining the parameters for the Perplexity chat model.
 */
export interface PerplexityChatInput extends BaseChatModelParams {
    /** Model name to use */
    model: string;
    /** Maximum number of tokens to generate */
    maxTokens?: number;
    /** Temperature parameter between 0 and 2 */
    temperature?: number;
    /** Top P parameter between 0 and 1 */
    topP?: number;
    /** Search domain filter - limit the citations used by the online model to URLs from the specified domains. */
    searchDomainFilter?: any[];
    /** Whether to return images */
    returnImages?: boolean;
    /** Determines whether or not a request to an online model should return related questions. */
    returnRelatedQuestions?: boolean;
    /** Returns search results within the specified time interval - does not apply to images. Values include month, week, day, hour. */
    searchRecencyFilter?: string;
    /** Top K parameter between 1 and 2048 */
    topK?: number;
    /** Presence penalty between -2 and 2 */
    presencePenalty?: number;
    /** Frequency penalty greater than 0 */
    frequencyPenalty?: number;
    /** API key for Perplexity.  Defaults to the value of
     * PERPLEXITY_API_KEY environment variable.
     */
    apiKey?: string;
    /** Whether to stream the results or not */
    streaming?: boolean;
    /** Timeout for requests to Perplexity */
    timeout?: number;
}
export interface PerplexityChatCallOptions extends BaseChatModelCallOptions {
    response_format?: {
        type: "json_schema";
        json_schema: {
            name: string;
            description: string;
            schema: Record<string, unknown>;
        };
    };
}
/**
 * Wrapper around Perplexity large language models that use the Chat endpoint.
 */
export declare class ChatPerplexity extends BaseChatModel<PerplexityChatCallOptions> implements PerplexityChatInput {
    static lc_name(): string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    apiKey?: string;
    timeout?: number;
    streaming?: boolean;
    topP?: number;
    searchDomainFilter?: any[];
    returnImages?: boolean;
    returnRelatedQuestions?: boolean;
    searchRecencyFilter?: string;
    topK?: number;
    presencePenalty?: number;
    frequencyPenalty?: number;
    private client;
    constructor(fields: PerplexityChatInput);
    _llmType(): string;
    /**
     * Get the parameters used to invoke the model
     */
    invocationParams(options?: this["ParsedCallOptions"]): {
        model: string;
        temperature: number | undefined;
        max_tokens: number | undefined;
        stream: boolean | undefined;
        top_p: number | undefined;
        return_images: boolean | undefined;
        return_related_questions: boolean | undefined;
        top_k: number | undefined;
        presence_penalty: number | undefined;
        frequency_penalty: number | undefined;
        response_format: {
            type: "json_schema";
            json_schema: {
                name: string;
                description: string;
                schema: Record<string, unknown>;
            };
        } | undefined;
        search_domain_filter: any[] | undefined;
        search_recency_filter: string | undefined;
    };
    /**
     * Convert a message to a format that the model expects
     */
    private messageToPerplexityRole;
    _generate(messages: BaseMessage[], options: this["ParsedCallOptions"], runManager?: CallbackManagerForLLMRun): Promise<ChatResult>;
    _streamResponseChunks(messages: BaseMessage[], options: this["ParsedCallOptions"], runManager?: CallbackManagerForLLMRun): AsyncGenerator<ChatGenerationChunk>;
    withStructuredOutput<RunOutput extends Record<string, any> = Record<string, any>>(outputSchema: InteropZodType<RunOutput> | Record<string, any>, config?: StructuredOutputMethodOptions<false>): Runnable<BaseLanguageModelInput, RunOutput>;
    withStructuredOutput<RunOutput extends Record<string, any> = Record<string, any>>(outputSchema: InteropZodType<RunOutput> | Record<string, any>, config?: StructuredOutputMethodOptions<true>): Runnable<BaseLanguageModelInput, {
        raw: BaseMessage;
        parsed: RunOutput;
    }>;
}
