import { LLM, type BaseLLMParams } from "@langchain/core/language_models/llms";
import { CallbackManagerForLLMRun } from "@langchain/core/callbacks/manager";
import { GenerationChunk } from "@langchain/core/outputs";
/**
 * @deprecated Install and import from "@langchain/cloudflare" instead.
 *
 * Interface for CloudflareWorkersAI input parameters.
 */
export interface CloudflareWorkersAIInput {
    cloudflareAccountId?: string;
    cloudflareApiToken?: string;
    model?: string;
    baseUrl?: string;
    streaming?: boolean;
}
/**
 * @deprecated Install and import from "@langchain/cloudflare" instead.
 *
 * Class representing the CloudflareWorkersAI language model. It extends the LLM (Large
 * Language Model) class, providing a standard interface for interacting
 * with the CloudflareWorkersAI language model.
 */
export declare class CloudflareWorkersAI extends LLM implements CloudflareWorkersAIInput {
    model: string;
    cloudflareAccountId?: string;
    cloudflareApiToken?: string;
    baseUrl: string;
    streaming: boolean;
    static lc_name(): string;
    lc_serializable: boolean;
    constructor(fields?: CloudflareWorkersAIInput & BaseLLMParams);
    /**
     * Method to validate the environment.
     */
    validateEnvironment(): void;
    /** Get the identifying parameters for this LLM. */
    get identifyingParams(): {
        model: string;
    };
    /**
     * Get the parameters used to invoke the model
     */
    invocationParams(): {
        model: string;
    };
    /** Get the type of LLM. */
    _llmType(): string;
    _request(prompt: string, options: this["ParsedCallOptions"], stream?: boolean): Promise<Response>;
    _streamResponseChunks(prompt: string, options: this["ParsedCallOptions"], runManager?: CallbackManagerForLLMRun): AsyncGenerator<GenerationChunk>;
    /** Call out to CloudflareWorkersAI's complete endpoint.
     Args:
         prompt: The prompt to pass into the model.
     Returns:
         The string generated by the model.
     Example:
     let response = CloudflareWorkersAI.call("Tell me a joke.");
     */
    _call(prompt: string, options: this["ParsedCallOptions"], runManager?: CallbackManagerForLLMRun): Promise<string>;
}
