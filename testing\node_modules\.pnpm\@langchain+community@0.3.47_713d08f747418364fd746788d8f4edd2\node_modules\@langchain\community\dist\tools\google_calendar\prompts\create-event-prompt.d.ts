export declare const CREATE_EVENT_PROMPT = "\nDate format: YYYY-MM-DDThh:mm:ss+00:00\nBased on this event description: \"Joey birthday tomorrow at 7 pm\",\noutput a JSON string of the following parameters. Do not include any other text or comments: \nToday's datetime on UTC time 2023-05-02T10:00:00+00:00, it's Tuesday and timezone\nof the user is -5, take into account the timezone of the user and today's date.\n1. event_summary \n2. event_start_time \n3. event_end_time \n4. event_location \n5. event_description \n6. user_timezone\noutput:\n{{\n    \"event_summary\": \"Joey birthday\",\n    \"event_start_time\": \"2023-05-03T19:00:00-05:00\",\n    \"event_end_time\": \"2023-05-03T20:00:00-05:00\",\n    \"event_location\": \"\",\n    \"event_description\": \"\",\n    \"user_timezone\": \"America/New_York\"\n}}\n\nDate format: YYYY-MM-DDThh:mm:ss+00:00\nBased on this event description: \"Create a meeting for 5 pm on Saturday with <PERSON>\",\noutput a JSON string of the following parameters. Do not include any other text or comments: \nToday's datetime on UTC time 2023-05-04T10:00:00+00:00, it's Thursday and timezone\nof the user is -5, take into account the timezone of the user and today's date.\n1. event_summary \n2. event_start_time \n3. event_end_time \n4. event_location \n5. event_description \n6. user_timezone\noutput:\n{{\n    \"event_summary\": \"Meeting with Joey\",\n    \"event_start_time\": \"2023-05-06T17:00:00-05:00\",\n    \"event_end_time\": \"2023-05-06T18:00:00-05:00\",\n    \"event_location\": \"\",\n    \"event_description\": \"\",\n    \"user_timezone\": \"America/New_York\"\n}}\n\nDate format: YYYY-MM-DDThh:mm:ss+00:00\nBased on this event description: \"{query}\", output a JSON string of the\nfollowing parameters. Do not include any other text or comments: \nToday's datetime on UTC time {date}, it's {dayName} and timezone of the user {u_timezone},\ntake into account the timezone of the user and today's date.\n1. event_summary \n2. event_start_time \n3. event_end_time \n4. event_location \n5. event_description \n6. user_timezone \noutput:\n";
