"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordManager = exports.UUIDV5_NAMESPACE = void 0;
var indexing_1 = require("@langchain/core/indexing");
Object.defineProperty(exports, "UUIDV5_NAMESPACE", { enumerable: true, get: function () { return indexing_1.UUIDV5_NAMESPACE; } });
Object.defineProperty(exports, "RecordManager", { enumerable: true, get: function () { return indexing_1.RecordManager; } });
