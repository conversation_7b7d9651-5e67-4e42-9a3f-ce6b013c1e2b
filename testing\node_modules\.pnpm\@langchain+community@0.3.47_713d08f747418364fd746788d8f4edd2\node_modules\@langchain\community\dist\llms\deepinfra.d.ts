import { LLM, type BaseLLMParams } from "@langchain/core/language_models/llms";
export declare const DEEPINFRA_API_BASE = "https://api.deepinfra.com/v1/openai/completions";
export declare const DEFAULT_MODEL_NAME = "mistralai/Mixtral-8x22B-Instruct-v0.1";
export declare const ENV_VARIABLE = "DEEPINFRA_API_TOKEN";
export interface DeepInfraLLMParams extends BaseLLMParams {
    apiKey?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
}
export declare class DeepInfraLLM extends LLM implements DeepInfraLLMParams {
    static lc_name(): string;
    lc_serializable: boolean;
    apiKey?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    constructor(fields?: Partial<DeepInfraLLMParams>);
    _llmType(): string;
    _call(prompt: string, options: this["ParsedCallOptions"]): Promise<string>;
}
