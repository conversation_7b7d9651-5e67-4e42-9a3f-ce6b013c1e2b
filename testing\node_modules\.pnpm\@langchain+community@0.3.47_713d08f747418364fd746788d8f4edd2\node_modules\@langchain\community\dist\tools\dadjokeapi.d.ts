import { Tool } from "@langchain/core/tools";
/**
 * The DadJokeAPI class is a tool for generating dad jokes based on a
 * specific topic. It fetches jokes from an external API and returns a
 * random joke from the results. If no jokes are found for the given
 * search term, it returns a message indicating that no jokes were found.
 */
declare class DadJokeAPI extends Tool {
    static lc_name(): string;
    name: string;
    description: string;
    /** @ignore */
    _call(input: string): Promise<string>;
}
export { DadJokeAPI };
